<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';
import { useAuthStore } from '~/stores/auth';
import { useNotificationStore } from '~/stores/notification';

useSeoMeta({
  title: 'Generate AI Videos | Veo 3',
  description:
    'Create stunning AI-generated videos with Veo 3. Transform your ideas into high-quality video content in seconds.',
  ogTitle: 'Generate AI Videos | Veo 3',
  ogDescription: 'Create professional videos instantly with AI',
});

const authStore = useAuthStore();
const notificationStore = useNotificationStore();
const { $api } = useNuxtApp();
const router = useRouter();

// Controls audio generation toggle state
const generateAudio = ref(true);

// Prompt for video description
const prompt = ref('');
const route = useRoute();

// 获取URL参数中的提示词
const urlPrompt = computed(() => route.query.prompt as string || '');

// 当URL参数变化时更新提示词输入框
watchEffect(() => {
  if (urlPrompt.value) {
    prompt.value = urlPrompt.value;
  }
});

// Simulated user credits
const userCredits = ref(0);
const generationCost = 100;

// Whether generation is in progress
const isGenerating = ref(false);

// Simulates video generation
async function generate() {
  if (!authStore.isAuthenticated) {
    authStore.toggleLoginModal();
    return;
  }

  if (!prompt.value) {
    notificationStore.showNotification('Prompt cannot be empty.', 'warning');
    return;
  }

  isGenerating.value = true;
  try {
    // 假设后端接口需要这样的 body 结构
    const requestBody = {
      prompt: prompt.value,
      generate_audio: generateAudio.value,
    };

    // 调用后端接口
    const response = await $api('/async_image/tasks/create_with_urls', {
      method: 'POST',
      body: requestBody,
    });

    notificationStore.showNotification('Video generation task started successfully!', 'success');
    authStore.fetchUser();
    router.push('/history');
  } catch (error) {
    console.error('Failed to start generation task:', error);
    notificationStore.showNotification('Failed to start generation task. Please try again.', 'error');
  } finally {
    isGenerating.value = false;
  }
}

// Resets the form
function resetForm() {
  prompt.value = '';
  generateAudio.value = true;
}
</script>

<template>
  <div class="generate-page">
    <div class="generate-container">
      <div class="input-section">
        <div class="controls">
          <p class="video-duration">Video duration: 8s</p>

          <div class="input-area">
            <label>Prompt</label>
            <textarea v-model="prompt" placeholder="Describe the video you want to generate..." rows="8"></textarea>
            <p class="input-hint">Provide detailed and specific descriptions of what you want to see in the video.</p>
          </div>

          <div class="audio-toggle-section">
            <div class="audio-label">
              <h4>Generate Audio</h4>
              <p>Generate natural and suitable audio for the output video.</p>
            </div>
            <label class="switch">
              <input v-model="generateAudio" type="checkbox" />
              <span class="slider round"></span>
            </label>
          </div>
        </div>

        <div class="action-bar">
          <button class="reset-btn" :disabled="isGenerating" @click="resetForm">Reset</button>
          <div class="generate-group">
            <div class="credit-info">
              <span>Credits: {{ userCredits }} remaining</span>
              <span>This generation will cost: {{ generationCost }} credits</span>
            </div>
            <button class="generate-btn" :disabled="isGenerating" @click="generate">
              <span v-if="isGenerating" class="spinner"></span>
              {{ isGenerating ? 'Generating...' : 'Generate Video' }}
            </button>
          </div>
        </div>
      </div>

      <div class="preview-section">
        <h3>Preview</h3>
        <div class="preview-box">
          <div class="preview-placeholder">
            <span class="preview-spinner"></span>
            <p>Ready to create your video</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Main Layout & Theme */
.generate-page {
  background-color: #121212;
  color: #e0e0e0;
  padding: 6rem 2rem 2rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.generate-container {
  display: grid;
  grid-template-columns: minmax(500px, 1.2fr) 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Left Column: Input Section */
.input-section {
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  border: 1px solid #282828;
  border-radius: 12px;
}

.controls {
  padding: 1.5rem;
  flex-grow: 1;
}

/* Input Areas */
.video-duration {
  color: #aaa;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.input-area {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: #ccc;
  font-weight: 500;
}

textarea {
  width: 100%;
  padding: 1rem;
  background-color: #101010;
  border: 1px solid #333;
  border-radius: 8px;
  color: #e0e0e0;
  font-size: 1rem;
  resize: vertical;
  transition: all 0.3s ease;
}

textarea:focus {
  outline: none;
  border-color: #7764E4;
}

.input-hint {
  font-size: 0.8rem;
  color: #777;
  margin-top: 0.5rem;
}

/* Audio Toggle */
.audio-toggle-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}
.audio-label h4 {
  margin: 0 0 0.25rem 0;
  font-weight: 500;
  color: #e0e0e0;
}
.audio-label p {
  margin: 0;
  font-size: 0.85rem;
  color: #888;
}
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #333;
  transition: 0.4s;
}
.slider:before {
  position: absolute;
  content: '';
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}
input:checked + .slider {
  background-color: #7764E4;
}
input:checked + .slider:before {
  transform: translateX(22px);
}
.slider.round {
  border-radius: 28px;
}
.slider.round:before {
  border-radius: 50%;
}

/* Action Bar */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid #282828;
  background-color: #1c1c1c;
  border-radius: 0 0 12px 12px;
}

.reset-btn {
  background-color: #282828;
  border: 1px solid #444;
  color: #ccc;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.reset-btn:hover:not(:disabled) {
  background-color: #333;
  border-color: #555;
}

.generate-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.credit-info {
  display: flex;
  flex-direction: column;
  text-align: right;
  font-size: 0.8rem;
  color: #888;
}
.credit-info span:first-child {
  color: #aaa;
}

.generate-btn {
  background-color: #7764E4;
  color: #121212;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.generate-btn:hover:not(:disabled) {
  filter: brightness(1.1);
}
.generate-btn:disabled,
.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  border-top-color: #121212;
  animation: spin 1s linear infinite;
}

/* Right Column: Preview Section */
.preview-section {
  background-color: #1a1a1a;
  border: 1px solid #282828;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}
.preview-section h3 {
  margin: 0 0 1rem 0;
  font-weight: 500;
  color: #ccc;
}
.preview-box {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #101010;
  border-radius: 8px;
}
.preview-placeholder {
  text-align: center;
  color: #888;
}
.preview-spinner {
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 4px solid #333;
  border-radius: 50%;
  border-top-color: #7764E4;
  animation: spin 1.5s linear infinite;
  margin-bottom: 1rem;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 1200px) {
  .generate-container {
    grid-template-columns: 1fr;
  }
}
/* Main Tabs Styles */
.main-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  border-bottom: 1px solid #282828;
}
.main-tabs button {
  padding: 0.75rem 2rem;
  background: none;
  border: none;
  color: #888;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  margin-bottom: -1px;
}
.main-tabs button.active {
  color: #e0e0e0;
  border-bottom-color: #e4b869;
}
/* History Section Styles */
.history-section {
  max-width: 1400px;
  margin: 0 auto;
}
.loading-state,
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #aaa;
}
.loading-state .spinner {
  width: 48px;
  height: 48px;
  border-width: 4px;
  border-color: rgba(255, 255, 255, 0.2);
  border-top-color: #e4b869;
  margin: 0 auto 1rem;
}
.empty-state h3 {
  font-size: 1.5rem;
  color: #e0e0e0;
  margin-bottom: 0.5rem;
}
.empty-state .primary-btn {
  margin-top: 1.5rem;
  background-color: #e4b869;
  color: #121212;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}
.empty-state .primary-btn:hover {
  filter: brightness(1.1);
}
.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}
.history-item {
  background-color: #1a1a1a;
  border: 1px solid #282828;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}
.history-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}
.video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%;
  background-color: #101010;
}
.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px 12px 0 0;
}
.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #888;
}
.video-placeholder .spinner {
  width: 32px;
  height: 32px;
  border-color: rgba(255, 255, 255, 0.2);
  border-top-color: #e4b869;
  margin-bottom: 0.5rem;
}
.task-info {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.prompt {
  color: #e0e0e0;
  font-size: 0.95rem;
  margin: 0 0 1rem 0;
  flex-grow: 1;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #888;
}
.status-badge {
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.75rem;
}
.status-completed {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}
.status-processing,
.status-pending {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}
.status-failed {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}
</style>
