<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue';
import { useRoute } from 'vue-router';
import { useRouter } from 'vue-router';
import { useAuthStore } from '~/stores/auth';
import { useNotificationStore } from '~/stores/notification';

useSeoMeta({
  title: 'Generate AI Videos | Veo 3',
  description:
    'Create stunning AI-generated videos with Veo 3. Transform your ideas into high-quality video content in seconds.',
  ogTitle: 'Generate AI Videos | Veo 3',
  ogDescription: 'Create professional videos instantly with AI',
});

const authStore = useAuthStore();
const notificationStore = useNotificationStore();
const { $api } = useNuxtApp();
const router = useRouter();

// Controls audio generation toggle state
const generateAudio = ref(true);

// Prompt for video description
const prompt = ref('');
const route = useRoute();

// 获取URL参数中的提示词
const urlPrompt = computed(() => (route.query.prompt as string) || '');

// 当URL参数变化时更新提示词输入框
watchEffect(() => {
  if (urlPrompt.value) {
    prompt.value = urlPrompt.value;
  }
});

// Simulated user credits
const userCredits = ref(0);
const generationCost = 100;

// Whether generation is in progress
const isGenerating = ref(false);

// Simulates video generation
async function generate() {
  if (!authStore.isAuthenticated) {
    authStore.toggleLoginModal();
    return;
  }

  if (!prompt.value) {
    notificationStore.showNotification('Prompt cannot be empty.', 'warning');
    return;
  }

  isGenerating.value = true;
  try {
    // 假设后端接口需要这样的 body 结构
    const requestBody = {
      prompt: prompt.value,
      generate_audio: generateAudio.value,
    };

    // 调用后端接口
    const response = await $api('/async_image/tasks/create_with_urls', {
      method: 'POST',
      body: requestBody,
    });

    notificationStore.showNotification('Video generation task started successfully!', 'success');
    authStore.fetchUser();
    router.push('/history');
  } catch (error) {
    console.error('Failed to start generation task:', error);
    notificationStore.showNotification('Failed to start generation task. Please try again.', 'error');
  } finally {
    isGenerating.value = false;
  }
}

// Resets the form
function resetForm() {
  prompt.value = '';
  generateAudio.value = true;
}
</script>

<template>
  <div class="generate-page">
    <div class="generate-container">
      <div class="input-section">
        <div class="controls">
          <p class="video-duration">Video duration: 8s</p>

          <div class="input-area">
            <label>Prompt</label>
            <textarea v-model="prompt" placeholder="Describe the video you want to generate..." rows="8"></textarea>
            <p class="input-hint">Provide detailed and specific descriptions of what you want to see in the video.</p>
          </div>

          <div class="audio-toggle-section">
            <div class="audio-label">
              <h4>Generate Audio</h4>
              <p>Generate natural and suitable audio for the output video.</p>
            </div>
            <label class="switch">
              <input v-model="generateAudio" type="checkbox" />
              <span class="slider round"></span>
            </label>
          </div>
        </div>

        <div class="action-bar">
          <button class="reset-btn" :disabled="isGenerating" @click="resetForm">Reset</button>
          <div class="generate-group">
            <div class="credit-info">
              <span>Credits: {{ userCredits }} remaining</span>
              <span>This generation will cost: {{ generationCost }} credits</span>
            </div>
            <button class="generate-btn" :disabled="isGenerating" @click="generate">
              <span v-if="isGenerating" class="spinner"></span>
              {{ isGenerating ? 'Generating...' : 'Generate Video' }}
            </button>
          </div>
        </div>
      </div>

      <div class="preview-section">
        <h3>Preview</h3>
        <div class="preview-box">
          <div class="preview-placeholder">
            <span class="preview-spinner"></span>
            <p>Ready to create your video</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Main Layout & Theme */
.generate-page {
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: #e0e0e0;
  padding: 6rem 2rem 2rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

.generate-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  z-index: 0;
}

.generate-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}

.generate-container {
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: minmax(500px, 1.2fr) 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Left Column: Input Section */
.input-section {
  display: flex;
  flex-direction: column;
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.controls {
  padding: 1.5rem;
  flex-grow: 1;
}

/* Input Areas */
.video-duration {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.input-area {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: #e0e0e0;
  font-weight: 500;
}

textarea {
  width: 100%;
  padding: 1rem;
  background-color: rgba(15, 15, 20, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #e0e0e0;
  font-size: 1rem;
  resize: vertical;
  transition: all 0.3s ease;
}

textarea:focus {
  outline: none;
  border-color: #06b6d4;
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
}

.input-hint {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.5rem;
}

/* Audio Toggle */
.audio-toggle-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}
.audio-label h4 {
  margin: 0 0 0.25rem 0;
  font-weight: 500;
  color: #e0e0e0;
}
.audio-label p {
  margin: 0;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: 0.4s;
}
.slider:before {
  position: absolute;
  content: '';
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}
input:checked + .slider {
  background-color: #06b6d4;
}
input:checked + .slider:before {
  transform: translateX(22px);
}
.slider.round {
  border-radius: 28px;
}
.slider.round:before {
  border-radius: 50%;
}

/* Action Bar */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(20, 20, 25, 0.8);
  border-radius: 0 0 16px 16px;
}

.reset-btn {
  background-color: rgba(40, 40, 45, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #e0e0e0;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.reset-btn:hover:not(:disabled) {
  background-color: rgba(50, 50, 55, 0.9);
  border-color: rgba(6, 182, 212, 0.5);
}

.generate-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.credit-info {
  display: flex;
  flex-direction: column;
  text-align: right;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}
.credit-info span:first-child {
  color: rgba(255, 255, 255, 0.8);
}

.generate-btn {
  background: linear-gradient(135deg, #06b6d4, #2dd4bf);
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 10px rgba(6, 182, 212, 0.3);
}
.generate-btn:hover:not(:disabled) {
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(6, 182, 212, 0.4);
}
.generate-btn:disabled,
.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(15, 23, 42, 0.3);
  border-radius: 50%;
  border-top-color: #0f172a;
  animation: spin 1s linear infinite;
}

/* Right Column: Preview Section */
.preview-section {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}
.preview-section h3 {
  margin: 0 0 1rem 0;
  font-weight: 500;
  color: #e0e0e0;
}
.preview-box {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(15, 15, 20, 0.8);
  border-radius: 8px;
  min-height: 300px;
}
.preview-placeholder {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}
.preview-spinner {
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: #06b6d4;
  animation: spin 1.5s linear infinite;
  margin-bottom: 1rem;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 1200px) {
  .generate-container {
    grid-template-columns: 1fr;
  }
}
</style>
