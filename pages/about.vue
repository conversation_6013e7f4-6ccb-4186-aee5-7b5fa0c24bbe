<script setup lang="ts">
useSeoMeta({
  title: 'About Our AI Video Generation Technology',
  description:
    'Learn about our cutting-edge AI video generation platform and how we help creators bring their ideas to life.',
  ogTitle: 'About Our AI Video Technology',
  ogDescription: 'Discover how our AI creates stunning videos from simple prompts',
});
</script>

<template>
  <div class="about-page">
    <div class="about-container">
      <div class="hero-section">
        <h1>About Veo 3</h1>
        <p class="subtitle">Revolutionizing video creation with cutting-edge AI technology</p>
      </div>

      <div class="content-section">
        <div class="feature-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <v-icon icon="fas fa-magic" size="x-large"></v-icon>
            </div>
            <h3>AI-Powered Generation</h3>
            <p>
              Transform your ideas into stunning videos using advanced artificial intelligence that understands context
              and creates professional-quality content.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <v-icon icon="fas fa-bolt" size="x-large"></v-icon>
            </div>
            <h3>Lightning Fast</h3>
            <p>
              Generate high-quality videos in seconds, not hours. Our optimized AI pipeline ensures rapid processing
              without compromising on quality.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <v-icon icon="fas fa-palette" size="x-large"></v-icon>
            </div>
            <h3>Creative Freedom</h3>
            <p>
              From simple concepts to complex narratives, our AI adapts to your creative vision and brings any idea to
              life with remarkable accuracy.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <v-icon icon="fas fa-users" size="x-large"></v-icon>
            </div>
            <h3>For Everyone</h3>
            <p>
              Whether you're a content creator, marketer, or business owner, Veo 3 makes professional video creation
              accessible to everyone.
            </p>
          </div>
        </div>

        <div class="mission-section">
          <h2>Our Mission</h2>
          <p>
            We believe that everyone should have access to powerful video creation tools. Veo 3 democratizes video
            production by making it as simple as describing what you want to see. Our mission is to empower creators,
            businesses, and individuals to tell their stories through the power of AI-generated video content.
          </p>
        </div>

        <div class="cta-section">
          <h2>Ready to Create?</h2>
          <p>Join thousands of creators who are already using Veo 3 to bring their ideas to life.</p>
          <NuxtLink to="/generate" class="cta-button">Start Creating Now</NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.about-page {
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: #e0e0e0;
  padding: 5rem 2rem 2rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

.about-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  z-index: 0;
}

.about-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}

.about-container {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
}

.hero-section {
  text-align: center;
  margin-bottom: 5rem;
}

h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #06b6d4, #2dd4bf);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.subtitle {
  font-size: 1.5rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2.5rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.content-section {
  max-width: 1200px;
  margin: 0 auto;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 5rem;
}

.feature-card {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border-color: rgba(6, 182, 212, 0.3);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(6, 182, 212, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.feature-icon .v-icon {
  color: #06b6d4;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #06b6d4;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

.mission-section {
  text-align: center;
  margin-bottom: 5rem;
  background-color: rgba(15, 15, 20, 0.4);
  border-radius: 16px;
  padding: 3rem 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.mission-section h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  background: linear-gradient(to right, #06b6d4, #2dd4bf);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.mission-section p {
  font-size: 1.2rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.8);
  max-width: 800px;
  margin: 0 auto;
}

.cta-section {
  text-align: center;
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 3rem 2rem;
  backdrop-filter: blur(5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.cta-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(to right, #06b6d4, #2dd4bf);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.cta-section p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
}

.cta-button {
  background: linear-gradient(135deg, #06b6d4, #2dd4bf);
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 4px 10px rgba(6, 182, 212, 0.3);
}

.cta-button:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .mission-section h2,
  .cta-section h2 {
    font-size: 2rem;
  }
}
</style>
