<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAuthStore } from '~/stores/auth';
import { useNotificationStore } from '~/stores/notification';

interface Task {
  id: string;
  prompt: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  output_file_url: string | null;
  created_at: string;
}

useSeoMeta({
  title: 'My Video History',
  description: 'View your generated AI video history',
  robots: 'noindex, nofollow',
});

const authStore = useAuthStore();
const notificationStore = useNotificationStore();
const { $api } = useNuxtApp();

const historyTasks = ref<Task[]>([]);
const isLoadingHistory = ref(true);

async function fetchHistory() {
  if (!authStore.isAuthenticated) {
    isLoadingHistory.value = false;
    return;
  }
  isLoadingHistory.value = true;
  try {
    const tasks = await $api<Task[]>('/async_image/tasks/list', {
      method: 'GET',
    });
    historyTasks.value = tasks.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } catch (error) {
    console.error('Failed to fetch history:', error);
    notificationStore.showNotification('Failed to load generation history.', 'error');
    historyTasks.value = [];
  } finally {
    isLoadingHistory.value = false;
  }
}

onMounted(() => {
  fetchHistory();
});

function formatStatus(status: string) {
  return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
}

function getStatusClass(status: string) {
  switch (status) {
    case 'COMPLETED':
      return 'status-completed';
    case 'PROCESSING':
    case 'PENDING':
      return 'status-pending';
    case 'FAILED':
      return 'status-failed';
    default:
      return '';
  }
}
</script>

<template>
  <div class="history-page">
    <div class="history-container">
      <div v-if="isLoadingHistory" class="loading-state">
        <div class="spinner"></div>
        <p>Loading your creations...</p>
      </div>
      <div v-else-if="!authStore.isAuthenticated || historyTasks.length === 0" class="empty-state">
        <h2>No Videos Yet</h2>
        <p>You haven't generated any videos. Start creating your first masterpiece!</p>
        <NuxtLink to="/generate" class="generate-btn">Generate Video</NuxtLink>
      </div>
      <div v-else class="history-grid">
        <div v-for="task in historyTasks" :key="task.id" class="history-item">
          <div class="video-container">
            <video v-if="task.output_file_url" :src="task.output_file_url" controls playsinline></video>
            <div v-else class="video-placeholder">
              <div class="spinner"></div>
              <span>{{ formatStatus(task.status) }}</span>
            </div>
          </div>
          <div class="task-info">
            <p class="prompt" :title="task.prompt">{{ task.prompt }}</p>
            <div class="task-meta">
              <span :class="['status-badge', getStatusClass(task.status)]">{{ formatStatus(task.status) }}</span>
              <span class="date">{{ new Date(task.created_at).toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.history-page {
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: #e0e0e0;
  padding: 6rem 2rem 2rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

.history-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  z-index: 0;
}

.history-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}
.history-container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
}
.loading-state,
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #aaa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 12rem);
}
.loading-state .spinner {
  width: 48px;
  height: 48px;
  border-width: 4px;
  border-color: rgba(255, 255, 255, 0.2);
  border-top-color: #06b6d4;
  margin: 0 auto 1rem;
  animation: spin 1.5s linear infinite;
}
.empty-state h2 {
  font-size: 2rem;
  color: #e0e0e0;
  margin-bottom: 1rem;
}
.empty-state p {
  font-size: 1.1rem;
  color: #aaa;
  margin-bottom: 2rem;
}
.generate-btn {
  background: linear-gradient(135deg, #06b6d4, #2dd4bf);
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 10px rgba(6, 182, 212, 0.3);
}
.generate-btn:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(6, 182, 212, 0.4);
}
.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}
.history-item {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  backdrop-filter: blur(5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}
.history-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border-color: rgba(6, 182, 212, 0.3);
}
.video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%;
  background-color: #101010;
}
.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px 12px 0 0;
}
.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #888;
}
.video-placeholder .spinner {
  width: 32px;
  height: 32px;
  border-color: rgba(255, 255, 255, 0.2);
  border-top-color: #06b6d4;
  margin-bottom: 0.5rem;
  animation: spin 1.5s linear infinite;
}
.task-info {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.prompt {
  color: #e0e0e0;
  font-size: 0.95rem;
  margin: 0 0 1rem 0;
  flex-grow: 1;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #888;
}
.status-badge {
  padding: 0.25rem 0.6rem;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.75rem;
}
.status-completed {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}
.status-processing,
.status-pending {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}
.status-failed {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
