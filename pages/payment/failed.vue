<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';

// 为这个错误页面设置 SEO 元信息，特别是 'noindex'
useSeoMeta({
  title: 'Payment Failed',
  robots: 'noindex, nofollow',
});

const route = useRoute();

// 从 URL 查询参数中获取订单号和错误信息以供显示
const orderNo = ref(route.query.order_no || 'N/A');
const errorMessage = ref(route.query.error || 'An unknown error occurred');
</script>

<template>
  <div class="payment-status-page">
    <div class="status-container failed">
      <div class="icon-container">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="80"
          height="80"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
      </div>
      <h2>Payment Failed</h2>
      <p>
        Unfortunately, we were unable to process your payment for order #{{ orderNo }}.
        <br />
        Reason: {{ errorMessage }}
      </p>
      <NuxtLink to="/pricing" class="cta-button">Try Again</NuxtLink>
    </div>
  </div>
</template>

<style scoped>
/* 这些样式与您的 success.vue 页面中的样式几乎完全相同，以保持一致性 */
.payment-status-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 6rem 2rem 2rem;
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: #e0e0e0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

.payment-status-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  z-index: 0;
}

.payment-status-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}
.status-container {
  text-align: center;
  max-width: 500px;
}
.icon-container {
  margin-bottom: 1.5rem;
}
.status-container.failed .icon-container {
  color: #cf6679; /* A suitable error color from your theme */
}
h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}
p {
  font-size: 1.2rem;
  color: #9e9e9e;
  margin-bottom: 2rem;
  line-height: 1.6;
}
.cta-button {
  background-color: #06b6d4;
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 10px rgba(6, 182, 212, 0.2);
}
.cta-button:hover {
  filter: brightness(1.1);
  box-shadow: 0 6px 15px rgba(6, 182, 212, 0.3);
}
</style>
