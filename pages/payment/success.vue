<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useNuxtApp } from '#app';
import { useAuthStore } from '~/stores/auth';
useSeoMeta({
  title: 'Payment Confirmation',
  robots: 'noindex, nofollow',
});
const route = useRoute();
const authStore = useAuthStore();
const { $api } = useNuxtApp();
const orderStatus = ref<'loading' | 'completed' | 'failed' | 'pending'>('loading');
const orderNo = ref(route.query.order_no || null);
onMounted(async () => {
  if (!orderNo.value) {
    orderStatus.value = 'failed';
    return;
  }
  try {
    const response = await $api<{ status: number }>(`/checkout/status/${orderNo.value}`);

    if (response.status === 1) {
      orderStatus.value = 'completed';
      // 支付成功，调用 action 来刷新用户信息（包括积分）
      await authStore.fetchUser();
    } else {
      orderStatus.value = 'pending';
    }
  } catch (error) {
    console.error('Failed to verify order status:', error);
    orderStatus.value = 'failed';
  }
});
</script>
<template>
  <div class="payment-status-page">
    <div v-if="orderStatus === 'loading'" class="status-container">
      <div class="icon-container">
        <span class="spinner"></span>
      </div>
      <h2>Verifying your payment...</h2>
      <p>Please wait while we confirm your transaction.</p>
    </div>

    <div v-else-if="orderStatus === 'completed'" class="status-container success">
      <div class="icon-container">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="80"
          height="80"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>
      </div>
      <h2>Payment Successful!</h2>
      <p>Your order ({{ orderNo }}) has been processed. Your new credits are now available.</p>
      <NuxtLink to="/history" class="cta-button">View My Content</NuxtLink>
    </div>

    <div v-else class="status-container failed">
      <div class="icon-container">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="80"
          height="80"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
      </div>
      <h2>Payment Verification Failed</h2>
      <p>There was an issue confirming your payment. If you believe this is an error, please contact support.</p>
      <NuxtLink to="/pricing" class="cta-button">Back to Pricing</NuxtLink>
    </div>
  </div>
</template>
<style scoped>
.payment-status-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 6rem 2rem 2rem;
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: #e0e0e0;
  overflow: hidden;
}

.payment-status-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  z-index: 0;
}

.payment-status-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}
.status-container {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 500px;
}
.icon-container {
  margin-bottom: 1.5rem;
}
.status-container.success .icon-container {
  color: #4caf50;
}
.status-container.failed .icon-container {
  color: #cf6679;
}
h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}
p {
  font-size: 1.2rem;
  color: #9e9e9e;
  margin-bottom: 2rem;
}
.cta-button {
  background-color: #06b6d4;
  color: #0f172a;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 10px rgba(6, 182, 212, 0.2);
}
.cta-button:hover {
  filter: brightness(1.1);
  box-shadow: 0 6px 15px rgba(6, 182, 212, 0.3);
}
.spinner {
  display: inline-block;
  width: 80px;
  height: 80px;
  border: 5px solid #333;
  border-radius: 50%;
  border-top-color: #06b6d4;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
