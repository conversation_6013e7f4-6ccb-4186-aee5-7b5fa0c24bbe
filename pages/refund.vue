<script setup lang="ts">
// 设置页面的SEO元信息，如标题和描述
useSeoMeta({
  title: 'Refund Policy | VEO3 AI',
  description:
    'Learn about the VEO3 AI refund policy for subscriptions and one-time credit purchases. We offer pro-rata refunds on subscriptions based on unused credits.',
  robots: 'noindex, follow', // 建议 'noindex' 因为这是法律/政策页面，对SEO排名意义不大
});
</script>

<template>
  <div class="policy-page">
    <div class="content-container">
      <h1>VEO3 AI Refund Policy</h1>
      <p class="last-updated">Last Updated: June 13, 2025</p>

      <p>
        Our goal at VEO3 AI is to provide a flexible and fair experience. This policy explains how we handle refunds for
        our subscriptions and credit purchases.
      </p>

      <h2>Subscription Refunds: Pay for What You Use</h2>
      <p>
        We believe you should only pay for the services you use. That's why we offer pro-rata refunds for our
        subscription plans based on your remaining credits. You can request a refund at any time during your billing
        cycle.
      </p>

      <h3>How It Works:</h3>
      <ul>
        <li>Your refund is calculated based on the percentage of unused credits in your subscription plan.</li>
        <li>
          <strong>Example:</strong> You purchase a monthly plan with 1,000 credits. You use 300 credits and decide to
          cancel. You have 700 credits (70% of your plan) remaining. We will refund 70% of your subscription fee, minus
          any standard processing fees.
        </li>
        <li>
          This policy applies to both monthly and annual subscriptions, giving you the flexibility to change your plan
          without being locked in.
        </li>
      </ul>

      <h2>One-Time Credit Pack Purchases</h2>
      <p>
        Please note that one-time purchases of credit packs are non-refundable. These packs are intended for users who
        need a specific number of credits without a recurring subscription. We encourage you to choose a pack that
        aligns with your needs, as all sales for these packs are final.
      </p>

      <h2>What Isn’t Refundable?</h2>
      <p>To maintain clarity, the following items are not eligible for a refund:</p>
      <ul>
        <li>
          <strong>Used Credits:</strong> The portion of your subscription fee that corresponds to credits you have
          already used.
        </li>
        <li><strong>One-Time Credit Packs:</strong> All sales are final.</li>
        <li>
          <strong>Past Subscription Periods:</strong> Fees for previous billing cycles that have already concluded.
        </li>
        <li><strong>Processing Fees:</strong> Transaction fees charged by our payment processor are non-refundable.</li>
      </ul>

      <h2>How to Request a Refund</h2>
      <p>To request a refund for an eligible subscription, please email our support team with the following details:</p>
      <ul>
        <li>Your account email address</li>
        <li>Order number or transaction ID</li>
        <li>The reason for your refund request</li>
      </ul>
      <p>
        Our team will review your request, calculate your eligible refund based on your remaining credits, and process
        it. Refunds are typically processed within 5-7 business days, though the exact time it appears in your account
        may depend on your bank or payment provider.
      </p>

      <h2>Policy Updates</h2>
      <p>
        We may update this Refund Policy from time to time to reflect changes in our services or for legal and
        regulatory reasons. We encourage you to review this page periodically.
      </p>

      <h2>Contact Us</h2>
      <p>If you have any questions about our Refund Policy, please don't hesitate to reach out to our support team.</p>
      <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>

      <p class="agreement">
        By using our services, you acknowledge that you have read and agree to this Refund Policy.
      </p>
    </div>
  </div>
</template>

<style scoped>
.policy-page {
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: #e0e0e0;
  padding: 8rem 2rem 4rem; /* 增加顶部内边距以避开固定头部 */
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

.policy-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  z-index: 0;
}

.policy-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}

.content-container {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.7;
}

h1,
h2 {
  background: linear-gradient(to right, #06b6d4, #2dd4bf);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(6, 182, 212, 0.2);
  padding-bottom: 0.5rem;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 1.8rem;
  margin-top: 3rem;
}

h3 {
  font-size: 1.4rem;
  color: #e0e0e0;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.last-updated,
.agreement {
  font-size: 0.9rem;
  color: #888;
  margin-top: -1rem;
  margin-bottom: 2rem;
}

.agreement {
  margin-top: 3rem;
  font-style: italic;
}

p {
  color: #ccc;
  margin-bottom: 1.5rem;
}

ul {
  list-style-type: disc;
  padding-left: 25px;
  margin-bottom: 1.5rem;
  color: #ccc;
}

li {
  margin-bottom: 0.75rem;
}

strong {
  color: #06b6d4;
  font-weight: 600;
}

a {
  color: #06b6d4;
  text-decoration: none;
  font-weight: 500;
  transition: text-decoration 0.3s ease;
}

a:hover {
  text-decoration: underline;
}
</style>
