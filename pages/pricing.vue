<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '~/stores/auth';
useSeoMeta({
  title: 'Pricing Plans | Veo 3',
  description:
    'Choose the perfect plan for your AI video generation needs. Flexible pricing for individuals and businesses.',
  ogTitle: 'Pricing Plans | Veo 3',
  ogDescription: 'Flexible pricing for AI video generation',
});

const isYearly = ref(false);
const isProcessing = ref(false);
const authStore = useAuthStore();
const { $api } = useNuxtApp(); // 在 setup 脚本的顶层获取 $api
const config = useRuntimeConfig().public;
// 帮助函数：将分转换为美元字符串
const formatPrice = (cents: number) => (cents / 100).toFixed(2).replace(/\.00$/, '');
// 帮助函数：格式化数字（加逗号）
const formatCredits = (credits: number) => credits.toLocaleString('en-US');
// 将从 env 读取的积分添加到每个套餐的特色列表中
const getCreditsFeature = (credits: number) => `${formatCredits(credits)} credits/month`;
const plans = [
  {
    id: 'basic',
    name: 'Basic',
    popular: false,
    prices: {
      monthly: parseInt(config.priceBasicMonthly as string),
      yearly: parseInt(config.priceBasicYearly as string),
    },
    features: [
      getCreditsFeature(parseInt(config.pointsPlanBasic as string)),
      'Access to all basic features',
      'Standard support',
      '720p resolution',
      'For personal use',
      'With Veo 3 watermark',
    ],
  },
  {
    id: 'plus',
    name: 'Plus',
    popular: true,
    prices: {
      monthly: parseInt(config.pricePlusMonthly as string),
      yearly: parseInt(config.pricePlusYearly as string),
    },
    features: [
      getCreditsFeature(parseInt(config.pointsPlanPlus as string)),
      'Access to all premium features',
      'Priority support',
      '1080p resolution',
      'For commercial use',
      'Custom watermark',
      'API access',
    ],
  },
  {
    id: 'pro',
    name: 'Pro',
    popular: false,
    prices: {
      monthly: parseInt(config.priceProMonthly as string),
      yearly: parseInt(config.priceProYearly as string),
    },
    features: [
      getCreditsFeature(parseInt(config.pointsPlanPro as string)),
      'Access to all professional features',
      'Dedicated account manager',
      '4K resolution',
      'For commercial use',
      'No watermark',
      'API access & team collaboration',
    ],
  },
];
async function handlePlanSelect(planId: 'basic' | 'plus' | 'pro') {
  console.log('Current token in store:', authStore.token);
  if (!authStore.isAuthenticated) {
    authStore.toggleLoginModal();
    return;
  }

  isProcessing.value = true;
  try {
    console.log('🏦 Starting checkout process...');
    const endpoint = '/checkout/create';
    const requestBody = {
      plan_name: planId,
      billing_cycle: isYearly.value ? 'yearly' : 'monthly',
    };

    console.log('📦 Request details:', {
      endpoint,
      method: 'POST',
      body: requestBody,
      hasToken: !!authStore.token,
    });

    console.log('Making API call to:', endpoint);
    const response = await $api<{ checkout_url: string; order_no: string }>(endpoint, {
      method: 'POST',
      body: requestBody,
    });

    console.log('💰 Checkout response:', response);

    window.location.href = response.checkout_url;
  } catch (error) {
    console.error('Failed to create checkout session:', error);
  } finally {
    isProcessing.value = false;
  }
}
</script>

<template>
  <div class="pricing-page">
    <div class="section-container">
      <h2>Pricing</h2>
      <p class="section-subtitle">Choose the plan that fits you best. Upgrade or cancel anytime.</p>

      <div class="billing-toggle-wrapper">
        <div class="billing-toggle" :class="{ 'yearly-active': isYearly }">
          <button :class="{ active: !isYearly }" @click="isYearly = false">Monthly</button>
          <button :class="{ active: isYearly }" @click="isYearly = true">Yearly</button>
        </div>
        <span class="discount-tag" :class="{ dimmed: !isYearly }" style="cursor: pointer" @click="isYearly = true">
          Save ~25%
        </span>
      </div>

      <div class="pricing-cards">
        <div v-for="plan in plans" :key="plan.id" class="card plan-card" :class="{ popular: plan.popular }">
          <span v-if="plan.popular" class="popular-badge">Recommended</span>
          <h4>{{ plan.name }}</h4>
          <div class="price-section">
            <span class="price"
              >${{ isYearly ? formatPrice(plan.prices.yearly / 12) : formatPrice(plan.prices.monthly) }}</span
            >
            <span class="billing-cycle">/ month</span>
          </div>
          <p v-if="isYearly" class="yearly-billing">Billed as ${{ formatPrice(plan.prices.yearly) }} per year</p>
          <p v-else class="yearly-billing">&nbsp;</p>
          <ul class="features">
            <li v-for="feature in plan.features" :key="feature">{{ feature }}</li>
          </ul>
          <button
            :class="plan.popular ? 'btn-primary' : 'btn-secondary'"
            @click="handlePlanSelect(plan.id as 'basic' | 'plus' | 'pro')"
            :disabled="isProcessing">
            {{ isProcessing ? 'Processing...' : 'Select' }}
          </button>
        </div>
      </div>
    </div>

    <div class="section-container">
      <h2>Pricing FAQ</h2>
      <p class="faq-subtitle">Get answers to common questions about Veo 3 pricing.</p>
      <div class="faq">
        <details>
          <summary>Can I cancel my subscription anytime?</summary>
          <p>Yes, you can cancel anytime. Your access will continue until the end of the current billing period.</p>
        </details>
        <details>
          <summary>How are credits calculated?</summary>
          <p>Please check the Generate page for credit calculation details.</p>
        </details>
        <details>
          <summary>Can I use generated videos for commercial purposes?</summary>
          <p>Yes, Plus and Pro plan subscribers have full commercial rights to use generated videos commercially.</p>
        </details>
        <details>
          <summary>What is your refund policy?</summary>
          <p>Please refer to the Refund Policy in the footer for details about our refund terms and conditions.</p>
        </details>
        <details>
          <summary>Do unused credits roll over to the next month?</summary>
          <p>
            Monthly credits from subscription plans reset at the end of each billing cycle and do not roll over.
            Purchased credit packs have no expiration.
          </p>
        </details>
        <details>
          <summary>Can I upgrade or downgrade my plan?</summary>
          <p>Yes, you can upgrade or downgrade your plan anytime in your account settings.</p>
        </details>
        <details>
          <summary>How do I get technical support?</summary>
          <p>Contact our customer service <NAME_EMAIL>. Our team will respond as soon as possible.</p>
        </details>
        <details>
          <summary>What if I use all my credits in a month?</summary>
          <p>You can purchase additional credit packs anytime or upgrade to a higher plan with more monthly credits.</p>
        </details>
      </div>
    </div>
  </div>
</template>

<style scoped>
.pricing-page {
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: var(--text-color);
  padding: 5rem 1rem 2rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

.pricing-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  z-index: 0;
}

.pricing-page::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}

h2 {
  background: linear-gradient(to right, #06b6d4, #2dd4bf);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.section-container {
  max-width: 1100px;
  margin: 0 auto 4rem auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.section-subtitle {
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

/* Billing Toggle Styles */
.billing-toggle-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 5rem;
  gap: 1.5rem;
}

.billing-toggle {
  display: inline-flex;
  background-color: rgba(40, 40, 45, 0.6);
  border: 1px solid var(--border-color);
  border-radius: 30px;
  padding: 6px;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.billing-toggle button {
  padding: 0.6rem 1.5rem;
  border: none;
  background-color: transparent;
  color: var(--text-color-secondary);
  cursor: pointer;
  border-radius: 25px;
  font-size: 0.95em;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  position: relative;
  z-index: 1;
}

.billing-toggle button.active {
  color: #121212;
  font-weight: 600;
}

.billing-toggle::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 6px;
  width: calc(50% - 6px);
  height: calc(100% - 12px);
  background: linear-gradient(135deg, #06b6d4, #2dd4bf);
  border-radius: 25px;
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
}

/* Control the position based on isYearly state */
.billing-toggle.yearly-active::after {
  transform: translateX(100%);
}

.discount-tag {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 0.95em;
  background-color: rgba(6, 182, 212, 0.1);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  border: 1px solid rgba(6, 182, 212, 0.3);
  transition: opacity 0.3s ease;
}
.discount-tag.dimmed {
  opacity: 0.6;
}

.pricing-cards {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: nowrap;
}

.card {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2.5rem 1.8rem;
  width: 330px;
  min-width: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border-color: rgba(6, 182, 212, 0.5);
}

.card.popular {
  border-color: #06b6d4;
  border-width: 2px;
  transform: scale(1.05);
  z-index: 2;
  box-shadow: 0 15px 40px rgba(6, 182, 212, 0.2);
}

.card.popular:hover {
  transform: scale(1.07) translateY(-5px);
}

.popular-badge {
  position: absolute;
  top: -18px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #06b6d4, #2dd4bf);
  color: #121212;
  padding: 0.4rem 1.2rem;
  border-radius: 20px;
  font-size: 0.95em;
  font-weight: bold;
  box-shadow: 0 4px 10px rgba(6, 182, 212, 0.4);
  z-index: 3;
}

.card h4 {
  font-size: 1.5rem;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 1rem;
}

.price-section {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 0.5rem;
}
.card .price {
  font-size: 2.8rem;
  font-weight: bold;
  color: var(--text-color);
}
.card .billing-cycle {
  font-size: 1rem;
  color: var(--text-color-secondary);
  margin-left: 0.5rem;
}
.card .yearly-billing {
  font-size: 0.9em;
  color: var(--text-color-secondary);
  height: 1.2em; /* Reserve space to prevent layout shift */
  margin-bottom: 1.5rem;
}

/* Plan Card Specific Styles */
.plan-card .features {
  list-style: none;
  padding: 0;
  text-align: left;
  margin: 0 0 2rem 0;
  flex-grow: 1;
  color: var(--text-color-secondary);
}

.plan-card .features li {
  margin-bottom: 1.2rem;
  display: flex;
  align-items: flex-start;
  font-size: 0.95rem;
  line-height: 1.4;
}

.plan-card .features li::before {
  content: '✓';
  color: var(--primary-color);
  margin-right: 10px;
  font-weight: bold;
  background: rgba(6, 182, 212, 0.15);
  min-width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

/* Credit Card Specific Styles */
.credit-card {
  align-items: center;
  position: relative;
  overflow: hidden;
}

.credit-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.5;
}

.credit-card .points {
  font-size: 3.5rem;
  font-weight: bold;
  background: linear-gradient(to bottom, #ffffff, #06b6d4);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin: 0.5rem 0 0 0;
  line-height: 1;
}

.credit-card .points-label {
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.credit-card .credit-price {
  font-size: 2.2rem;
  font-weight: 600;
  margin-top: auto;
  margin-bottom: 2rem;
}

/* Button Styles */
button {
  width: 100%;
  padding: 1rem;
  border-radius: 10px;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: auto;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, #06b6d4, #2dd4bf);
  color: #0f172a;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.btn-primary:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
}

.btn-secondary {
  background-color: rgba(40, 40, 45, 0.7);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: rgba(50, 50, 55, 0.9);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
}

/* FAQ Styles */
.faq-subtitle {
  color: var(--text-color-secondary);
  margin-top: 0.5rem;
  margin-bottom: 2rem;
}
.faq {
  max-width: 800px;
  margin: 3rem auto 0 auto;
  text-align: left;
}

.faq details {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  margin-bottom: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.faq details[open] {
  border-left: 3px solid var(--primary-color);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.faq summary {
  font-weight: 500;
  cursor: pointer;
  color: var(--text-color);
  font-size: 1.1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  outline: none;
}

.faq summary::-webkit-details-marker {
  display: none;
}

.faq summary::after {
  content: '+';
  font-size: 1.5rem;
  transition: transform 0.3s ease;
  color: var(--primary-color);
}

.faq details[open] summary::after {
  content: '−';
  transform: rotate(90deg);
}

.faq p {
  margin-top: 1.2rem;
  color: var(--text-color-secondary);
  padding-left: 0;
  line-height: 1.6;
  font-size: 0.95rem;
}

@media (max-width: 992px) {
  .card.popular {
    transform: scale(1.03);
  }
  .card.popular:hover {
    transform: scale(1.05) translateY(-5px);
  }
}

@media (max-width: 768px) {
  .pricing-page {
    padding: 2rem 1rem;
  }

  h2 {
    font-size: 2rem;
  }

  .pricing-cards {
    flex-direction: column;
    align-items: center;
    gap: 2.5rem;
  }

  .card {
    width: 100%;
    max-width: 380px;
  }

  .card.popular {
    transform: scale(1);
    order: -1;
  }

  .card.popular:hover {
    transform: translateY(-10px);
  }

  .billing-toggle-wrapper {
    flex-direction: column;
    margin-bottom: 3rem;
  }
}
</style>
